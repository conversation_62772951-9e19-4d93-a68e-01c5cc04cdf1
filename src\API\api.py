"""API web para exponer servicios de pruebas y automatizaciu00f3n."""

import os
import sys
import asyncio
import concurrent.futures
from typing import Dict, List, Optional, Any
from datetime import datetime
from dotenv import load_dotenv

from fastapi import FastAPI, HTTPException, Depends, Body, File, UploadFile, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field

# Importar las nuevas rutas modulares
from src.API.project_routes import router as project_router
from src.API.suite_routes import router as suite_router
from src.API.testcase_routes import router as testcase_router

# Importar modelos desde el archivo separado
from src.API.models import (
    SmokeTestRequest,
    FullTestRequest,
    GherkinRequest,
    CodeGenerationRequest,
    EnhanceStoryRequest,
    GenerateManualTestsRequest,
    GenerateGherkinRequest,
    SaveHistoryRequest
)

# Cargar variables de entorno
load_dotenv()

# Importar el servicio central
from src.Core.test_service import TestService

# Verificar que la API key estu00e9 configurada
if not os.environ.get("GOOGLE_API_KEY"):
    raise ValueError("No se encontru00f3 la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")

# Crear la aplicaciu00f3n FastAPI
app = FastAPI(
    title="QA Agent API",
    description="API para automatizaciu00f3n de pruebas, generaciu00f3n de casos de prueba y gestiou00f3n de proyectos.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS para permitir solicitudes desde cualquier origen
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permitir todos los oru00edgenes en desarrollo
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir las rutas modulares
app.include_router(project_router)
app.include_router(suite_router)
app.include_router(testcase_router)

# Funciou00f3n para obtener una instancia del servicio de pruebas
def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

# Función helper para ejecutar tests en un proceso separado
def run_smoke_test_sync(instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un smoke test de manera síncrona en un proceso separado."""
    try:
        # Configurar la política de bucle de eventos para Windows
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

        # Crear un nuevo bucle de eventos
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Crear el servicio de pruebas
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

            # Crear el escenario Gherkin
            gherkin_scenario = test_service.create_gherkin_scenario(
                instructions=instructions,
                url=url,
                user_story=user_story
            )

            # Ejecutar el test
            result = loop.run_until_complete(
                test_service.test_executor.execute_smoke_test(
                    gherkin_scenario=gherkin_scenario,
                    url=url
                )
            )

            # El history ya viene como diccionario desde test_executor.py
            # No necesitamos convertirlo nuevamente
            if "history" in result and result["history"]:
                # Verificar si ya es un diccionario o si necesita conversión
                history = result["history"]
                if hasattr(history, 'urls'):  # Es un objeto AgentHistoryList
                    result["history"] = {
                        "urls": history.urls() if hasattr(history, 'urls') else [],
                        "action_names": history.action_names() if hasattr(history, 'action_names') else [],
                        "extracted_content": history.extracted_content() if hasattr(history, 'extracted_content') else [],
                        "errors": history.errors() if hasattr(history, 'errors') else [],
                        "final_result": history.final_result() if hasattr(history, 'final_result') else None,
                        "model_actions": history.model_actions() if hasattr(history, 'model_actions') else []
                    }
                # Si ya es un diccionario, no hacer nada

            return result

        finally:
            loop.close()

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Función helper para ejecutar full tests en un proceso separado
def run_full_test_sync(gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un full test de manera síncrona en un proceso separado."""
    try:
        # Configurar la política de bucle de eventos para Windows
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

        # Crear un nuevo bucle de eventos
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Crear el servicio de pruebas
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

            # Ejecutar el test
            result = loop.run_until_complete(
                test_service.test_executor.execute_full_test(
                    gherkin_scenario=gherkin_scenario,
                    url=url
                )
            )

            # El history ya viene como diccionario desde test_executor.py para smoke tests
            # Para full tests puede venir como objeto AgentHistoryList
            if "history" in result and result["history"]:
                # Verificar si ya es un diccionario o si necesita conversión
                history = result["history"]
                if hasattr(history, 'urls'):  # Es un objeto AgentHistoryList
                    result["history"] = {
                        "urls": history.urls() if hasattr(history, 'urls') else [],
                        "action_names": history.action_names() if hasattr(history, 'action_names') else [],
                        "extracted_content": history.extracted_content() if hasattr(history, 'extracted_content') else [],
                        "errors": history.errors() if hasattr(history, 'errors') else [],
                        "final_result": history.final_result() if hasattr(history, 'final_result') else None,
                        "model_actions": history.model_actions() if hasattr(history, 'model_actions') else []
                    }
                # Si ya es un diccionario, no hacer nada

            return result

        finally:
            loop.close()

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Los modelos Pydantic ahora se importan desde src.API.models

# Rutas de API para pruebas
@app.post("/api/tests/smoke", summary="Ejecutar smoke test")
async def run_smoke_test(request: SmokeTestRequest):
    """Ejecuta un smoke test con las instrucciones proporcionadas."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_smoke_test_sync,
                request.instructions,
                request.url,
                request.user_story
            )

        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/tests/full", summary="Ejecutar test completo")
async def run_full_test(request: FullTestRequest):
    """Ejecuta un test completo con el escenario Gherkin proporcionado."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_full_test_sync,
                request.gherkin_scenario,
                request.url
            )

        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/gherkin", summary="Generar escenario Gherkin")
async def create_gherkin_scenario(
    request: GherkinRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera un escenario Gherkin a partir de instrucciones."""
    try:
        gherkin = test_service.create_gherkin_scenario(
            instructions=request.instructions,
            url=request.url,
            user_story=request.user_story
        )
        return {"gherkin": gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/code", summary="Generar cu00f3digo de automatizaciu00f3n")
async def generate_code(
    request: CodeGenerationRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera cu00f3digo de automatizaciu00f3n para un framework especu00edfico."""
    try:
        code = test_service.generate_code(
            framework=request.framework,
            gherkin_scenario=request.gherkin_scenario,
            test_history=request.test_history
        )
        return {"code": code}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Rutas de API para agentes de historias
@app.post("/api/stories/enhance", summary="Mejorar historia de usuario")
async def enhance_story(
    request: EnhanceStoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Mejora una historia de usuario."""
    try:
        # Creamos un StoryAgent a travu00e9s de TestService
        from src.Agents.agents import StoryAgent
        story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
        enhanced_story = story_agent.enhance_story(request.user_story)
        return {"enhanced_story": enhanced_story}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-manual-tests", summary="Generar casos de prueba manuales")
async def generate_manual_tests(
    request: GenerateManualTestsRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera casos de prueba manuales a partir de una historia mejorada."""
    try:
        # Creamos un StoryAgent a través de TestService
        from src.Agents.agents import StoryAgent
        story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
        manual_tests = story_agent.generate_manual_tests(request.enhanced_story)
        return {"manual_tests": manual_tests}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-gherkin", summary="Generar escenarios Gherkin desde casos manuales")
async def generate_gherkin_from_manual(
    request: GenerateGherkinRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera escenarios Gherkin a partir de casos de prueba manuales."""
    try:
        # Creamos un StoryAgent a través de TestService
        from src.Agents.agents import StoryAgent
        story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
        gherkin = story_agent.generate_gherkin(request.manual_tests)
        return {"gherkin": gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/projects/save-history", summary="Guardar historial en proyecto")
async def save_history_to_project(
    request: SaveHistoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Guarda un historial de prueba en un proyecto."""
    try:
        test_case = test_service.save_history_to_project(
            project_id=request.project_id,
            suite_id=request.suite_id,
            test_history=request.test_history,
            name=request.name,
            description=request.description,
            gherkin=request.gherkin
        )
        return {"test_case": test_case}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Endpoint para verificar el estado de la API
@app.get("/api/health", summary="Verificar estado de la API")
async def get_status():
    """Verifica el estado de la API y devuelve informacion basica."""
    return {
        "status": "online",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "api_key_configured": bool(os.environ.get("GOOGLE_API_KEY"))
    }

# Punto de entrada para ejecutar la aplicaciu00f3n
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)